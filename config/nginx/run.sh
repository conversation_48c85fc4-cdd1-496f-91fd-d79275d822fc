#!/usr/bin/env bash

if [ -z "${SOFTGATE_HOST}" ]; then
  export SOFTGATE_HOST='unknown'
fi

if [ -z "${SOFTGATE_MANAGEMENT_API}" ]; then
  export SOFTGATE_MANAGEMENT_API="${MANAGEMENT_API}"
fi

mkdir -p conf.d
envsubst '$MANAGEMENT_API $SOFTGATE_MANAGEMENT_API $BRIDGE_URL $LOGIN_URL $CASINO_HUB_URL $ENGAGEMENT_HUB_URL $DATA_HUB_URL $STUDIO_HUB_URL $ENV_NAME $LOCATION_NAME $SOFTGATE_HOST $SOFTGATE_BRIDGE_URL $SOFTGATE_LOGIN_URL $SOFTGATE_CASINO_HUB_URL $SOFTGATE_ENGAGEMENT_HUB_URL $SOFTGATE_DATA_HUB_URL $SOFTGATE_STUDIO_HUB_URL' < /usr/share/nginx/default.conf.tpl > /etc/nginx/conf.d/default.conf
nginx -g "daemon off;"
